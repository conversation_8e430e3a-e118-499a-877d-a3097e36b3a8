import { useState } from 'react';
import { Modal, Form, Input, message, Select } from 'antd';
import {createGroup} from 'COMMON/api/qe_rag/workgroup';
import styles from './common.module.less';

const { TextArea } = Input;
const { Option } = Select;

const CreateGroupDialog = ({ 
  visible, 
  onCancel, 
  onSubmitSuccess 
}) => {
  const [form] = Form.useForm();
  const [loading, setLoading] = useState(false);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      setLoading(true);
      
      await createGroup({
        name: values.name,
        business: values.business,
        manager: values.manager,
        createUser: values.createUser || '当前用户' // 这里应该从用户状态中获取
      });
      
      message.success('工作组创建成功');
      form.resetFields();
      onCancel();
      onSubmitSuccess?.();
    } catch (error) {
      console.error('创建工作组失败:', error);
      message.error('创建工作组失败');
    } finally {
      setLoading(false);
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      title="创建工作组"
      open={visible}
      onCancel={handleCancel}
      onOk={handleSubmit}
      confirmLoading={loading}
      okText="创建"
      cancelText="取消"
      width={500}
    >
      <Form
        form={form}
        layout="vertical"
        initialValues={{
          createUser: '当前用户' // 这里应该从用户状态中获取
        }}
      >
        <Form.Item
          name="name"
          label="工作组名称"
          rules={[
            { required: true, message: '请输入工作组名称' },
            { max: 50, message: '工作组名称不能超过50个字符' }
          ]}
        >
          <Input placeholder="请输入工作组名称" />
        </Form.Item>

        <Form.Item
          name="business"
          label="事业群"
          rules={[
            { required: true, message: '请输入事业群名称' },
            { max: 50, message: '事业群名称不能超过50个字符' }
          ]}
        >
          <Input placeholder="请输入事业群名称" />
        </Form.Item>

        <Form.Item
          name="manager"
          label="经理"
          rules={[
            { required: true, message: '请输入经理名称' },
            { max: 50, message: '经理名称不能超过50个字符' }
          ]}
        >
          <Input placeholder="请输入经理名称" />
        </Form.Item>

        <Form.Item
          name="createUser"
          label="创建者"
        >
          <Input disabled placeholder="当前用户" />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateGroupDialog;