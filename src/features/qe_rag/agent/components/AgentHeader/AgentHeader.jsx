import { But<PERSON>, <PERSON>, <PERSON>, Modal } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useNavigate } from 'umi';
import styles from './AgentHeader.module.less';

const AgentHeader = ({
    isLoadingFaBu,
    isLoadingUpdate,
    onSave,
    onUpdate,
    hasUnsavedChanges = false
}) => {
    const navigate = useNavigate();

    const handleGoBack = () => {
        // if (hasUnsavedChanges) {
        //     Modal.confirm({
        //         title: '确认离开',
        //         content: '您有未保存的更改，确定要离开吗？',
        //         okText: '确认离开',
        //         cancelText: '取消',
        //         okType: 'danger',
        //         onOk: () => {
        //             navigate('/qe_rag/agent');
        //         }
        //     });
        // } else {
            navigate('/qe_rag/agent');
        // }
    };
    return (
        <Row className={styles.upperRow}>
            <Col span={24}>
                <Row className={styles.headerContent}>
                    <Col span={2} className={styles.backButton}>
                        <Button
                            type="text"
                            icon={<ArrowLeftOutlined />}
                            onClick={handleGoBack}
                            className={styles.backBtn}
                        >
                            返回
                        </Button>
                    </Col>
                    <Col span={4} className={styles.headerTitle}>
                        <span>我的Agent应用</span>
                    </Col>
                    <Col span={14}></Col>
                    <Col span={4} className={styles.headerActions}>
                        {isLoadingFaBu && (
                            <Button
                                type="primary"
                                className={styles.publishBtn}
                                onClick={onSave}
                            >
                                发布
                            </Button>
                        )}
                        {isLoadingUpdate && (
                            <Button
                                type="primary"
                                className={styles.publishBtn}
                                onClick={onUpdate}
                            >
                                更新
                            </Button>
                        )}
                    </Col>
                </Row>
            </Col>
        </Row>
    );
};

export default AgentHeader;
